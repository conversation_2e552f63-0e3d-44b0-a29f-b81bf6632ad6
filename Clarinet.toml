[project]
name = 'clearbounty'
description = ''
authors = []
telemetry = true
cache_dir = './.cache'
requirements = []
[contracts.application]
path = 'contracts/application.clar'
clarity_version = 3
epoch = 3.1

[contracts.bounty-manager]
path = 'contracts/bounty-manager.clar'
clarity_version = 3
epoch = 3.1

[contracts.dispute-resolver]
path = 'contracts/dispute-resolver.clar'
clarity_version = 3
epoch = 3.1

[contracts.escrow]
path = 'contracts/escrow.clar'
clarity_version = 3
epoch = 3.1

[contracts.governance-dao]
path = 'contracts/governance-dao.clar'
clarity_version = 3
epoch = 3.1

[contracts.oracle-adapter]
path = 'contracts/oracle-adapter.clar'
clarity_version = 3
epoch = 3.1

[contracts.reputation-token]
path = 'contracts/reputation-token.clar'
clarity_version = 3
epoch = 3.1
[repl.analysis]
passes = ['check_checker']

[repl.analysis.check_checker]
strict = false
trusted_sender = false
trusted_caller = false
callee_filter = false
