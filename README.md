# ClearBounty
**Decentralized freelance bounty marketplace on Stacks blockchain**

## Table of Contents
1. [Introduction](#introduction)
2. [Features](#features)
3. [Getting Started](#getting-started)
4. [Usage](#usage)
5. [Testing](#testing)
6. [Contributing](#contributing)
7. [License](#license)

## Introduction
ClearBounty solves the problem of centralized freelance platforms by providing a transparent, on-chain bounty system where:
- Clients post tasks with escrowed payments
- Freelancers compete fairly
- Disputes resolved via decentralized oracles
- Reputation system tokenizes work history

## Features
- **Bounty Manager**: Create/manage bounties
- **Application System**: Freelancer submissions
- **Escrow**: Secure payment handling
- **Reputation Token**: SIP-010 token for work history
- **Dispute Resolver**: Oracle-powered conflict resolution
- **Governance DAO**: Community-controlled upgrades

## Getting Started
### Prerequisites
- Clarinet v1.0+
- Node.js v16+

### Installation
```bash
git clone https://github.com/clearbounty/clearbounty
cd clearbounty
clarinet install